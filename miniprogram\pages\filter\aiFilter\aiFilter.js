// 智能筛选页面
const filterApi = require('../../../api/filter');

Page({
  /**
   * 页面的初始数据
   */
  data: {
    // 用户输入的筛选需求
    userInput: '',
    
    // 加载状态
    isLoading: false,
    
    // 结果展示
    showResult: false,
    result: null,
    
    // 错误状态
    showError: false,
    errorMessage: '',
    
    // 示例数据
    examples: [
      '找出市盈率低于20倍，净资产收益率大于15%的科技股',
      '筛选股息率大于3%，负债率小于50%的银行股',
      '市值超过100亿，营收增长率大于20%的消费股',
      '市净率小于2倍，毛利率大于30%的制造业公司',
      '现金流充足，ROE大于12%的白马股'
    ]
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    console.log('智能筛选页面加载');
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {
    console.log('智能筛选页面渲染完成');
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    console.log('智能筛选页面显示');
  },

  /**
   * 输入框内容变化
   */
  onInputChange(e) {
    this.setData({
      userInput: e.detail.value,
      showError: false // 清除错误状态
    });
  },

  /**
   * 使用示例
   */
  useExample(e) {
    const example = e.currentTarget.dataset.example;
    this.setData({
      userInput: example,
      showError: false,
      showResult: false
    });
  },

  /**
   * 生成筛选脚本
   */
  async generateScript() {
    const { userInput } = this.data;
    
    if (!userInput.trim()) {
      wx.showToast({
        title: '请输入筛选需求',
        icon: 'none'
      });
      return;
    }

    this.setData({
      isLoading: true,
      showError: false,
      showResult: false
    });

    try {
      console.log('开始生成AI筛选脚本，用户输入：', userInput);
      
      const response = await filterApi.generateAiFilterScript(userInput);
      
      console.log('AI筛选脚本生成成功：', response);
      
      if (response.success) {
        this.setData({
          result: response.data,
          showResult: true,
          isLoading: false
        });
        
        wx.showToast({
          title: '生成成功',
          icon: 'success'
        });
      } else {
        throw new Error(response.message || '生成失败');
      }
    } catch (error) {
      console.error('生成AI筛选脚本失败：', error);
      
      this.setData({
        showError: true,
        errorMessage: error.message || '网络错误，请重试',
        isLoading: false
      });
      
      wx.showToast({
        title: '生成失败',
        icon: 'none'
      });
    }
  },

  /**
   * 执行脚本
   */
  async executeScript(e) {
    const fileName = e.currentTarget.dataset.filename;
    
    if (!fileName) {
      wx.showToast({
        title: '脚本文件名错误',
        icon: 'none'
      });
      return;
    }

    wx.showLoading({
      title: '执行中...'
    });

    try {
      console.log('开始执行脚本：', fileName);
      
      // 这里先使用沙箱执行，如果需要可以改为 executeScriptRequire
      const response = await filterApi.executeScriptSandbox(fileName);
      
      console.log('脚本执行成功：', response);
      
      wx.hideLoading();
      
      if (response.success) {
        wx.showToast({
          title: '执行成功',
          icon: 'success'
        });
        
        // TODO: 这里可以跳转到结果页面或者展示执行结果
        // 暂时先显示成功提示
        console.log('脚本执行结果：', response.data);
        
      } else {
        throw new Error(response.message || '执行失败');
      }
    } catch (error) {
      console.error('执行脚本失败：', error);
      
      wx.hideLoading();
      wx.showToast({
        title: error.message || '执行失败',
        icon: 'none'
      });
    }
  },

  /**
   * 重新生成
   */
  regenerateScript() {
    this.setData({
      showResult: false,
      showError: false,
      result: null
    });
    
    // 重新生成
    this.generateScript();
  },

  /**
   * 重试生成
   */
  retryGenerate() {
    this.generateScript();
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    // 下拉刷新时重置页面状态
    this.setData({
      userInput: '',
      showResult: false,
      showError: false,
      result: null,
      isLoading: false
    });
    
    wx.stopPullDownRefresh();
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    return {
      title: '言策AI智能筛选 - 用自然语言筛选股票',
      path: '/pages/filter/aiFilter/aiFilter'
    };
  }
});
