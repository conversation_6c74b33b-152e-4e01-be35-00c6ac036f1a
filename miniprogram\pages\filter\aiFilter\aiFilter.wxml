<!--智能筛选页面-->
<view class="ai-filter-page">
  <!-- 页面标题 -->
  <view class="page-header">
    <view class="title-section">
      <text class="page-title">智能筛选</text>
      <text class="page-subtitle">用自然语言描述您的筛选需求，AI为您生成专业的筛选脚本</text>
    </view>
  </view>

  <!-- 智能输入区域 -->
  <view class="ai-input-section">
    <view class="input-container">
      <view class="input-header">
        <text class="input-title">描述您的筛选需求</text>
        <text class="input-subtitle">例如：找出市盈率低于20倍，净资产收益率大于15%的科技股</text>
      </view>
      
      <view class="input-area">
        <textarea 
          class="ai-input"
          placeholder="请用自然语言描述您的筛选条件..."
          value="{{userInput}}"
          bindinput="onInputChange"
          maxlength="1000"
          auto-height
          show-confirm-bar="{{false}}"
        />
        <view class="input-footer">
          <text class="char-count">{{userInput.length}}/1000</text>
        </view>
      </view>
    </view>

    <!-- 示例区域 -->
    <view class="examples-section" wx:if="{{!userInput.trim()}}">
      <text class="examples-title">💡 试试这些示例：</text>
      <view class="examples-list">
        <view 
          class="example-item" 
          wx:for="{{examples}}" 
          wx:key="index"
          bindtap="useExample" 
          data-example="{{item}}"
        >
          <text class="example-text">{{item}}</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 生成按钮 -->
  <view class="generate-section">
    <button 
      class="btn-generate {{!userInput.trim() || isLoading ? 'disabled' : ''}}"
      bindtap="generateScript"
      disabled="{{!userInput.trim() || isLoading}}"
    >
      <text class="btn-text">{{isLoading ? '正在生成中...' : '🚀 生成筛选脚本'}}</text>
      <view class="loading-spinner" wx:if="{{isLoading}}"></view>
    </button>
  </view>

  <!-- 结果展示区域 -->
  <view class="result-section" wx:if="{{showResult}}">
    <view class="result-header">
      <text class="result-title">✨ 生成结果</text>
    </view>
    
    <!-- 逻辑分析 -->
    <view class="analysis-card" wx:if="{{result.logicAnalysis}}">
      <view class="card-header">
        <text class="card-title">🧠 逻辑分析</text>
      </view>
      <view class="card-content">
        <text class="analysis-text">{{result.logicAnalysis}}</text>
      </view>
    </view>

    <!-- 生成的脚本列表 -->
    <view class="scripts-card" wx:if="{{result.scripts && result.scripts.length > 0}}">
      <view class="card-header">
        <text class="card-title">📝 生成的脚本 ({{result.scripts.length}}个)</text>
      </view>
      <view class="scripts-list">
        <view 
          class="script-item" 
          wx:for="{{result.scripts}}" 
          wx:key="fileName"
          wx:for-item="script"
        >
          <view class="script-header">
            <text class="script-name">{{script.fileName}}</text>
            <text class="script-desc">{{script.description}}</text>
          </view>
          <view class="script-actions">
            <button 
              class="btn-execute"
              bindtap="executeScript"
              data-filename="{{script.fileName}}"
            >
              执行脚本
            </button>
          </view>
        </view>
      </view>
    </view>

    <!-- 重新生成按钮 -->
    <view class="regenerate-section">
      <button class="btn-regenerate" bindtap="regenerateScript">
        🔄 重新生成
      </button>
    </view>
  </view>

  <!-- 错误提示 -->
  <view class="error-section" wx:if="{{showError}}">
    <view class="error-card">
      <text class="error-icon">⚠️</text>
      <text class="error-title">生成失败</text>
      <text class="error-message">{{errorMessage}}</text>
      <button class="btn-retry" bindtap="retryGenerate">重试</button>
    </view>
  </view>

  <!-- 帮助提示 -->
  <view class="help-section">
    <view class="help-card">
      <text class="help-title">💡 使用提示</text>
      <view class="help-content">
        <text class="help-item">• 尽量使用具体的数值和条件，如"市盈率小于20"</text>
        <text class="help-item">• 可以组合多个条件，如"高ROE且低负债率的公司"</text>
        <text class="help-item">• 支持行业筛选，如"银行股"、"科技股"等</text>
        <text class="help-item">• AI会根据您的描述生成相应的筛选脚本</text>
      </view>
    </view>
  </view>
</view>
