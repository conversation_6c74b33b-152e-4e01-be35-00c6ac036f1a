/**
 * AI自动生成的筛选脚本 (勿直接在生产执行前未审核情况下使用)
 * 生成时间: 2025-09-25T01:58:09.603Z
 * 逻辑索引: 2
 * 逻辑类型: trend_analysis
 * 复杂度评估: medium
 * 逻辑原始描述: 分析每只股票近三年ROE的变化趋势，判断是否呈现上升态势
 *
 * 使用说明:
 *  1. 可通过 require 动态载入后在沙箱中执行(当前执行服务支持字符串形式)。
 *  2. 如需持久化版本控制，请将本文件纳入 git 并代码审查。
 *  3. 修改本文件后请注意不要引入非白名单模块。
 */

const executeLogic = async () => {
  try {
    const currentYear = 2024;
    const startYear = currentYear - 2;
    
    const result = await FinancialData.aggregate([
      {
        $match: {
          reportType: "按年度",
          fiscalYear: { $gte: startYear, $lte: currentYear }
        }
      },
      {
        $group: {
          _id: "$stockCode",
          roeData: {
            $push: {
              year: "$fiscalYear",
              roe: "$data.keyMetrics.returnOnEquity"
            }
          }
        }
      },
      {
        $project: {
          stockCode: "$_id",
          roeData: 1,
          isRisingTrend: {
            $let: {
              vars: {
                sortedData: {
                  $sortArray: {
                    input: "$roeData",
                    sortBy: { year: 1 }
                  }
                }
              },
              in: {
                $and: [
                  { $gt: [{ $arrayElemAt: ["$$sortedData.roe", 2] }, { $arrayElemAt: ["$$sortedData.roe", 1] }] },
                  { $gt: [{ $arrayElemAt: ["$$sortedData.roe", 1] }, { $arrayElemAt: ["$$sortedData.roe", 0] }] }
                ]
              }
            }
          }
        }
      },
      {
        $match: {
          "isRisingTrend": true,
          "roeData.roe": { $ne: null }
        }
      },
      {
        $project: {
          _id: 0,
          stockCode: 1,
          roeTrend: "$roeData"
        }
      }
    ]);
    
    return result;
  } catch (error) {
    throw new Error(`查询失败: ${error.message}`);
  }
};
