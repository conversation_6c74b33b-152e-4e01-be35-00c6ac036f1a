const { generateTextWithDeepSeek } = require('../../utils/deepseekApi');
const DailyStockData = require('../../models/DailyStockData');
const FinancialData = require('../../models/FinancialData');
const StockBasicInfo = require('../../models/StockBasicInfo');
// 引入数据模型摘要信息
const { 
  dailyStockDataModelInfo,
  financialDataModelInfo,
  stockBasicInfoModelInfo
} = require('../../config/databaseAbstract');
// 新增: 用于保存生成脚本到本地文件
const fs = require('fs');
const path = require('path');

// 生成脚本保存的基础目录（位于 server/scripts/generatedFilters 下）
// 当前文件路径: server/src/services/filter/aiFilterScriptGeneratorService.js
// 因此需要返回 3 层到 server 根目录: ../../../
const GENERATED_BASE_DIR = path.join(__dirname, '../../../scripts/generatedFilters');

// 确保目录存在（同步创建一次即可，不影响主流程）
const ensureGeneratedDirExists = () => {
  try {
    if (!fs.existsSync(GENERATED_BASE_DIR)) {
      fs.mkdirSync(GENERATED_BASE_DIR, { recursive: true });
      console.log('[AI脚本生成] 已创建目录:', GENERATED_BASE_DIR);
    }
  } catch (e) {
    console.warn('[AI脚本生成] 创建目录失败(不影响继续返回脚本字符串):', e.message);
  }
};

// 生成安全的文件名：包含索引、时间戳与描述摘要（只保留字母数字与下划线，中文与其他字符剔除）
const buildScriptFileName = (logicIndex, description) => {
  const timeStr = new Date().toISOString().replace(/[-:TZ.]/g, '').slice(0, 14); // 精确到小时: YYYYMMDDHHMMSS
  const raw = (description || 'logic').toString().slice(0, 30); // 取前 30 字符
  const ascii = raw
    .replace(/[\u4e00-\u9fa5]/g, '') // 去掉中文（简单处理）
    .replace(/[^a-zA-Z0-9]+/g, '_') // 非法字符转下划线
    .replace(/_+/g, '_')
    .replace(/^_|_$/g, '')
    .toLowerCase();
  const slug = ascii || 'logic';
  return `ai_filter_${logicIndex}_${timeStr}_${slug}.js`;
};

// 写入脚本文件（异步）。若失败，返回 { saved: false, error }
const writeScriptFile = async ({ logicIndex, logicDescription, logicType, complexity, scriptCode }) => {
  ensureGeneratedDirExists();
  const fileName = buildScriptFileName(logicIndex, logicDescription);
  const filePath = path.join(GENERATED_BASE_DIR, fileName);

  // 文件头部注释，保留业务语义，便于后期手工调整与版本管理
  const header = `/**\n * AI自动生成的筛选脚本 (勿直接在生产执行前未审核情况下使用)\n * 生成时间: ${new Date().toISOString()}\n * 逻辑索引: ${logicIndex}\n * 逻辑类型: ${logicType}\n * 复杂度评估: ${complexity}\n * 逻辑原始描述: ${logicDescription}\n *\n * 使用说明:\n *  1. 可通过 require 动态载入后在沙箱中执行(当前执行服务支持字符串形式)。\n *  2. 如需持久化版本控制，请将本文件纳入 git 并代码审查。\n *  3. 修改本文件后请注意不要引入非白名单模块。\n */\n\n`;

  // let finalCode = scriptCode.trim();

  // // 若不存在 module.exports 但包含 executeLogic 定义，则自动追加导出统一格式
  // if (/function\s+executeLogic|const\s+executeLogic|async\s+function\s+executeLogic/.test(finalCode) && !/module\.exports\s*=/.test(finalCode)) {
  //   finalCode += '\n\n// 统一导出格式，便于直接require执行\nmodule.exports = { executeLogic };\n';
  // }

  const content = header + scriptCode + (scriptCode.endsWith('\n') ? '' : '\n');

  try {
    await fs.promises.writeFile(filePath, content, { encoding: 'utf8', flag: 'w' });
    console.log('[AI脚本生成] 已保存脚本到文件:', filePath);
    return { saved: true, filePath, fileName };
  } catch (err) {
    console.warn('[AI脚本生成] 保存脚本文件失败，将仅返回代码字符串:', err.message);
    return { saved: false, filePath, fileName, error: err.message };
  }
};

/**
 * AI智能生成筛选脚本服务
 * 通过两个Agent实现：第一个Agent解析用户逻辑，第二个Agent生成可执行脚本
 * @param {string} userInput 用户输入的筛选需求描述
 * @returns {Promise<Object>} 包含子逻辑和完整脚本的结果
 */
const generateFilterScript = async (userInput) => {
  try {
    console.log('开始AI智能生成筛选脚本...');
    console.log('用户输入:', userInput);

    // 第一个Agent：解析用户逻辑，判断是否需要拆分
    const logicAnalysis = await parseUserLogicToSubLogics(userInput);
    console.log('逻辑分析结果:', logicAnalysis.needSplit ? '需要拆分' : '不需要拆分');

    // 第二个Agent：根据逻辑分析结果生成可执行脚本
    const executableScripts = await generateExecutableScripts(logicAnalysis);
    console.log('生成的脚本数量:', executableScripts.length);

    // 返回结果
    return {
      success: true,
      userInput,
      logicAnalysis,
      executableScripts,
      timestamp: new Date().toISOString()
    };

  } catch (error) {
    console.error('AI智能生成筛选脚本失败:', error);
    throw new Error(`AI智能生成筛选脚本失败: ${error.message}`);
  }
};

/**
 * 第一个Agent：解析用户逻辑，判断是否需要拆分成子逻辑
 * @param {string} userInput 用户输入
 * @returns {Promise<Object>} 逻辑分析结果
 */
const parseUserLogicToSubLogics = async (userInput) => {
  try {
    // 构建第一个Agent的系统提示词
    const systemPrompt = buildLogicParserSystemPrompt();

    // 构建用户提示词
    const userPrompt = buildLogicParserUserPrompt(userInput);

    // 调用DeepSeek API
    const response = await generateTextWithDeepSeek(userPrompt, systemPrompt, {
      temperature: 0.0,
      maxTokens: 2000
    });

    // 解析AI返回的逻辑分析
    const logicAnalysis = parseSubLogicsResponse(response);

    return logicAnalysis;
  } catch (error) {
    throw new Error(`解析用户逻辑失败: ${error.message}`);
  }
};

/**
 * 第二个Agent：根据逻辑分析结果生成可执行脚本
 * @param {Object} logicAnalysis 逻辑分析结果
 * @returns {Promise<Array>} 可执行脚本数组
 */
const generateExecutableScripts = async (logicAnalysis) => {
  try {
    if (!logicAnalysis.needSplit) {
      // 不需要拆分，生成单个脚本（保持原有行为）
      console.log('生成单个脚本:', logicAnalysis.singleLogic.description);
      const single = await generateSingleExecutableScript(logicAnalysis.singleLogic, 1);
      return [single];
    }

    // 需要拆分：并行为每个子逻辑生成脚本
    // 子逻辑相互独立，可并行提升总体吞吐
    const subLogics = logicAnalysis.subLogics || [];
    if (subLogics.length === 0) return [];

    console.log(`检测到需要拆分的子逻辑数量: ${subLogics.length}，开始并行生成...`);

    const tasks = subLogics.map((subLogic, idx) => {
      const logicIndex = idx + 1;
      console.log(`(排队) 子逻辑 ${logicIndex} -> ${subLogic.description}`);
      return generateSingleExecutableScript(subLogic, logicIndex)
        .then(result => {
          console.log(`(完成) 子逻辑 ${logicIndex} 脚本生成成功`);
          return result;
        });
    });

    // 如果需要容错（部分成功），可改 Promise.allSettled；当前保持全失败即抛错策略
    const scripts = await Promise.all(tasks);
    return scripts;
  } catch (error) {
    throw new Error(`生成可执行脚本失败: ${error.message}`);
  }
};

/**
 * 为单个逻辑生成可执行脚本
 * @param {Object} logicInfo 逻辑信息对象
 * @param {number} index 逻辑索引
 * @returns {Promise<Object>} 可执行脚本对象
 */
const generateSingleExecutableScript = async (logicInfo, index) => {
  try {
    // 构建第二个Agent的系统提示词 (新增: 仅注入需要的数据表摘要)
    const systemPrompt = buildScriptGeneratorSystemPrompt(logicInfo.usedDatasets);

    // 构建用户提示词
    const userPrompt = buildScriptGeneratorUserPrompt(logicInfo);

    // 调用DeepSeek API
    const response = await generateTextWithDeepSeek(userPrompt, systemPrompt, {
      temperature: 0.0,
      maxTokens: 3000
    });

    // 解析AI返回的脚本代码
    const scriptCode = parseScriptResponse(response);

    // 保存到文件系统
    const logicType = logicInfo.type || 'simple_filter';
    const complexity = logicInfo.complexity || 'low';
    const saveResult = await writeScriptFile({
      logicIndex: index,
      logicDescription: logicInfo.description,
      logicType,
      complexity,
      scriptCode
    });

    return {
      logicIndex: index,
      logicDescription: logicInfo.description,
      logicType,
      complexity,
      // scriptCode: scriptCode,
      fileName: saveResult.fileName,
      filePath: saveResult.filePath,
      fileSaved: saveResult.saved,
      fileSaveError: saveResult.saved ? null : saveResult.error || null,
      generatedAt: new Date().toISOString()
    };
  } catch (error) {
    throw new Error(`为逻辑 ${index} 生成脚本失败: ${error.message}`);
  }
};

/**
 * 构建第一个Agent的系统提示词
 * @returns {string} 系统提示词
 */
const buildLogicParserSystemPrompt = () => {
  // === 新增：引入三个模型的字段映射，帮助 Agent 了解可用数据域 ===
  const dailyMapping = typeof DailyStockData.getFieldMapping === 'function' ? DailyStockData.getFieldMapping() : {};
  const financialMapping = typeof FinancialData.getFieldMapping === 'function' ? FinancialData.getFieldMapping() : {};
  const basicMapping = typeof StockBasicInfo.getFieldMapping === 'function' ? StockBasicInfo.getFieldMapping() : {};
  // 年度数据存在滞后：FinancialData 年度数据只到上一完整年度
  const currentYear = new Date().getFullYear();
  const lastCompleteYear = currentYear - 1; // 最新完整年度

  const formatMapping = (mappingObj) => {
    return Object.entries(mappingObj)
      .map(([cn, en]) => `- ${cn} => ${en}`)
      .join('\n');
  };

  const dailyMappingStr = formatMapping(dailyMapping);
  const financialMappingStr = formatMapping(financialMapping);
  const basicMappingStr = formatMapping(basicMapping);

  return `你是一个专业的股票筛选逻辑分析专家。你的任务：读取用户的自然语言筛选/计算需求，判断是否需要拆分成多个“子逻辑”以便后续生成可执行脚本。

为提升你对需求复杂度的判断，下面提供当前系统中可用的数据库字段映射（中文 -> 英文字段标识）。

================ 可用数据表字段 ================
【dailyStockData 当日数据】(短周期/行情 / 估值 / 交易特征)
${dailyMappingStr}

【financialData 财务年度/报告期数据】(中长期经营 / 盈利能力 / 资产负债 / 现金流 / 成长性)
${financialMappingStr}

【stockBasicInfo 基本信息】(静态属性 / 行业 / 板块 / 上市时间)
${basicMappingStr}
================================================================================

【年度数据滞后规则（必须遵守并在分析中内化）】
1. FinancialData 年度数据存在披露滞后：当前自然年 ${currentYear} 尚无完整年度数据，最新可用完整年度 = ${lastCompleteYear}。
2. 用户使用“今年 / 本年度 / ${currentYear} 年” 描述年度财务指标时，应自动解释为“上一完整年度 ${lastCompleteYear}”。
3. “最近N年” 序列需从 ${lastCompleteYear} 向前推 N-1 年；例：最近3年 => ${lastCompleteYear}, ${lastCompleteYear - 1}, ${lastCompleteYear - 2}。
4. 严禁引用未来或未完成年度 (${currentYear}) 的年度汇总值；如用户显式提及，需在子逻辑描述中注明已替换为 ${lastCompleteYear}。
5. 若需求需要体现当前年度进展，应提示改用日级或报告期(季度/半年度)数据，而非年度汇总。


**什么情况需要拆分子逻辑：**
1. 需要复杂计算的逻辑（如：计算多年平均值、增长率趋势、形态识别等）
2. 需要多步骤处理的逻辑（如：先筛选A条件，再基于结果计算B指标）
3. 涉及跨时间段的复杂分析（如：连续N年满足某条件的变化趋势）
4. 需要自定义指标计算的逻辑（如：自定义评分、综合排名等）

**什么情况不需要拆分：**
1. 简单的条件筛选（如：市盈率在某个范围、行业筛选、单一指标比较）
2. 基本的数据查询（如：连续三年净利润大于0，这只是简单的多年数据筛选）
3. 直接的字段比较（如：收盘价大于某值、成交量在某范围）

【输出要求】
如果不需要拆分，返回：
{
  "needSplit": false,
  "reason": "不需要拆分的原因(中文)",
  "singleLogic": {
    "description": "对整体需求的精炼业务描述",
    "type": "simple_filter",
    "complexity": "low",
    "usedDatasets": ["dailyStockData", "financialData"] //表示该该逻辑中的计算和筛选需要用到的数据表
  }
}

如果需要拆分，返回：
{
  "needSplit": true,
  "reason": "需要拆分的原因(中文)",
  "subLogics": [
    {
      "description": "子逻辑的业务与分析意图描述",
      "type": "calculation|complex_filter|ranking|trend_analysis",
      "complexity": "medium|high",
      "usedDatasets": ["financialData"] //表示该子逻辑中的计算和筛选需要用到的数据表
    }
  ]
}

**重要原则：**
1. 优先考虑不拆分，只有真正复杂的逻辑才拆分
2. 子逻辑描述要用自然语言，不要包含具体的字段名和操作符
3. 每个子逻辑应该代表一个独立的业务概念,子逻辑要相互没有依赖关系
4. 拆分后的子逻辑应该比原始需求更容易理解和实现

请严格只输出 JSON。`;
};

/**
 * 构建第一个Agent的用户提示词
 * @param {string} userInput 用户输入
 * @returns {string} 用户提示词
 */
const buildLogicParserUserPrompt = (userInput) => {
  return `请分析以下用户的股票筛选需求，判断是否需要拆分成子逻辑：

用户需求："${userInput}"

请仔细分析：
1. 这个需求是否涉及复杂计算或多步骤处理？
2. 是否可以通过简单的数据库查询直接实现？
3. 是否需要自定义指标计算或复杂的业务逻辑？

如果是简单的筛选条件（如：连续三年净利润大于0、市盈率在某范围、行业筛选等），请不要拆分。
只有真正复杂的逻辑才需要拆分成子逻辑。

只返回JSON格式，不要任何额外说明。`;
};

/**
 * 构建第二个Agent的系统提示词
 * @returns {string} 系统提示词
 */
const buildScriptGeneratorSystemPrompt = (usedDatasets) => {
  // 直接使用数据库摘要信息（包含字段说明与示例）
  const dailyFields = dailyStockDataModelInfo;
  const financialFields = financialDataModelInfo;
  const basicInfoFields = stockBasicInfoModelInfo;
  // 年度滞后：FinancialData 年度数据只到上一完整年度
  const currentYear = new Date().getFullYear();
  const lastCompleteYear = currentYear - 1;

  // 新增: 根据 usedDatasets 过滤需要的表。若未提供或为空则回退使用全部，确保向后兼容。
  const VALID_KEYS = ['dailyStockData','financialData','stockBasicInfo'];
  let ds = Array.isArray(usedDatasets) ? usedDatasets.filter(v => VALID_KEYS.includes(v)) : [];
  if (ds.length === 0) ds = VALID_KEYS; // 回退

  const parts = [];
  if (ds.includes('dailyStockData')) {
    parts.push(`**1. DailyStockData (当日股票数据表)：**\n${dailyFields}`);
  }
  if (ds.includes('financialData')) {
    parts.push(`**2. FinancialData (财务数据表)：**\n${financialFields}`);
  }
  if (ds.includes('stockBasicInfo')) {
    parts.push(`**3. StockBasicInfo (股票基本信息表)：**\n${basicInfoFields}`);
  }
  const fieldBlock = parts.join('\n\n');

  return `你是一个专业的MongoDB查询脚本生成专家。你的任务是根据给定的逻辑描述生成完整的、可执行的JavaScript代码。

**可用的数据表和字段：**
${fieldBlock}

【年度数据滞后约束（必须在代码逻辑中严格遵守）】
1. 财务年度数据存在披露滞后：当前自然年 ${currentYear} 的年度汇总尚未完整，最新可用完整年度 = ${lastCompleteYear}。
2. 用户要求 “今年 / 本年度 / ${currentYear} 年” 的年度指标时，代码中必须改用 ${lastCompleteYear} 年度数据字段/过滤。
3. “最近N年” 序列起点为 ${lastCompleteYear}，向前推 N-1 年；不得生成含 ${currentYear} 年的年度判断。
4. 若需体现当前年份进展，应改用日级(DailyStockData)或报告期数据（若后续提供），禁止伪造未披露年度值。
5. 任何循环或聚合年份逻辑需显式排除 > ${lastCompleteYear} 的年度。

**你需要生成的代码特点：**
1. 完整的可执行JavaScript函数
2. 使用Mongoose进行数据库查询
3. 包含错误处理
4. 返回符合条件的股票列表
5. 代码简洁高效，只包含核心逻辑
6. 根据逻辑描述选择合适的字段和操作符

**代码模板结构：**
\`\`\`javascript
const executeLogic = async () => {
  try {
    // 核心查询逻辑
    const result = await Model.find(conditions);
    return result;
  } catch (error) {
    throw new Error(\`查询失败: \${error.message}\`);
  }
};
\`\`\`

**重要要求：**
1. 根据逻辑描述选择正确的数据表和字段
2. 使用正确的MongoDB查询语法和操作符
3. 对于跨年度的财务数据查询，使用聚合管道或多次查询，并注意年度滞后规则
4. 确保代码可以直接运行
5. 包含基本的错误处理
6. 只返回JavaScript代码，不要任何解释或markdown标记`;
};

/**
 * 构建第二个Agent的用户提示词
 * @param {Object} logicInfo 逻辑信息对象
 * @returns {string} 用户提示词
 */
const buildScriptGeneratorUserPrompt = (logicInfo) => {
  return `请为以下逻辑描述生成完整的可执行JavaScript代码：

逻辑描述：${logicInfo.description}
逻辑类型：${logicInfo.type || 'simple_filter'}

请仔细分析这个逻辑描述，选择合适的数据表、字段和查询条件，生成一个完整的JavaScript函数。

特别注意：
1. 对于"连续N年"的条件，需要查询多个年度的财务数据
2. 选择最合适的字段名（参考上面的字段列表）
3. 使用正确的MongoDB查询语法
4. 确保查询逻辑符合业务需求

只返回JavaScript代码，不要任何额外说明或markdown标记。`;
};

/**
 * 解析逻辑分析响应
 * @param {string} response AI返回的响应
 * @returns {Object} 解析结果
 */
const parseSubLogicsResponse = (response) => {
  try {
    console.log('AI返回的逻辑分析:', response);

    // 去除可能的Markdown代码块标记
    let cleanResponse = response.trim();
    if (cleanResponse.startsWith('```json')) {
      cleanResponse = cleanResponse.replace(/^```json\s*/, '');
    }
    if (cleanResponse.startsWith('```')) {
      cleanResponse = cleanResponse.replace(/^```\s*/, '');
    }
    if (cleanResponse.endsWith('```')) {
      cleanResponse = cleanResponse.replace(/\s*```$/, '');
    }

    // 解析JSON
    const parsed = JSON.parse(cleanResponse);

    // 验证必要字段
    if (typeof parsed.needSplit !== 'boolean') {
      throw new Error('返回结果必须包含needSplit字段');
    }

    return parsed;
  } catch (error) {
    throw new Error(`无法解析AI返回的逻辑分析: ${error.message}。返回内容: ${response}`);
  }
};

/**
 * 解析脚本响应
 * @param {string} response AI返回的响应
 * @returns {string} 脚本代码
 */
const parseScriptResponse = (response) => {
  try {
    // console.log('AI返回的脚本代码:', response);

    // 去除可能的Markdown代码块标记
    let cleanResponse = response.trim();
    if (cleanResponse.startsWith('```javascript')) {
      cleanResponse = cleanResponse.replace(/^```javascript\s*/, '');
    }
    if (cleanResponse.startsWith('```js')) {
      cleanResponse = cleanResponse.replace(/^```js\s*/, '');
    }
    if (cleanResponse.startsWith('```')) {
      cleanResponse = cleanResponse.replace(/^```\s*/, '');
    }
    if (cleanResponse.endsWith('```')) {
      cleanResponse = cleanResponse.replace(/\s*```$/, '');
    }

    // 验证代码是否包含基本的函数结构
    if (!cleanResponse.includes('async') || !cleanResponse.includes('await')) {
      console.warn('生成的代码可能不包含异步函数结构');
    }

    return cleanResponse;
  } catch (error) {
    throw new Error(`无法解析AI返回的脚本代码: ${error.message}。返回内容: ${response}`);
  }
};

module.exports = {
  generateFilterScript
};
