{"testCase": "testCase3", "description": "找出近三年净利润增长率平均超过20%，且ROE呈上升趋势的股票", "timestamp": "2025-09-25T01:58:09.606Z", "result": {"success": true, "userInput": "找出近三年净利润增长率平均超过20%，且ROE呈上升趋势的股票", "logicAnalysis": {"needSplit": true, "reason": "需求涉及两个复杂计算：1) 需要计算近三年净利润增长率的平均值；2) 需要分析ROE的上升趋势，这需要多步骤处理和趋势判断", "subLogics": [{"description": "计算每只股票近三年净利润增长率的平均值，并筛选出平均值超过20%的股票", "type": "calculation", "complexity": "medium", "usedDatasets": ["financialData"]}, {"description": "分析每只股票近三年ROE的变化趋势，判断是否呈现上升态势", "type": "trend_analysis", "complexity": "medium", "usedDatasets": ["financialData"]}]}, "executableScripts": [{"logicIndex": 1, "logicDescription": "计算每只股票近三年净利润增长率的平均值，并筛选出平均值超过20%的股票", "logicType": "calculation", "complexity": "medium", "fileName": "ai_filter_1_20250925015803_2.js", "filePath": "D:\\investment-AI\\server\\scripts\\generatedFilters\\ai_filter_1_20250925015803_2.js", "fileSaved": true, "fileSaveError": null, "generatedAt": "2025-09-25T01:58:03.891Z"}, {"logicIndex": 2, "logicDescription": "分析每只股票近三年ROE的变化趋势，判断是否呈现上升态势", "logicType": "trend_analysis", "complexity": "medium", "fileName": "ai_filter_2_20250925015809_roe.js", "filePath": "D:\\investment-AI\\server\\scripts\\generatedFilters\\ai_filter_2_20250925015809_roe.js", "fileSaved": true, "fileSaveError": null, "generatedAt": "2025-09-25T01:58:09.604Z"}], "timestamp": "2025-09-25T01:58:09.605Z"}}